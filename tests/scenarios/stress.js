import http from 'k6/http';
import { check, sleep } from 'k6';
import { config } from '../config/config.js';
import { generateRandomEndpoint, createRampUpScenario, logTestSummary } from '../utils/helpers.js';

// Stress test - Beyond normal capacity to find breaking point
export const options = {
  scenarios: createRampUpScenario('stress_test', 200, '15m'), // Max 200 VUs for 15 minutes
  thresholds: config.thresholds.stress,
  tags: {
    testType: 'stress'
  }
};

export default function () {
  const url = generateRandomEndpoint();
  
  const response = http.get(url, {
    timeout: config.timeout,
    tags: { 
      endpoint: url.split('/').slice(-2).join('/'),
      vu: __VU,
      iteration: __ITER
    }
  });
  
  // More lenient checks for stress test
  check(response, {
    'Status is not 5xx': (r) => r.status < 500,
    'Response time < 5000ms': (r) => r.timings.duration < 5000,
    'No connection errors': (r) => r.status !== 0,
  });
  
  // Log errors for analysis
  if (response.status >= 400) {
    console.log(`Error ${response.status} for ${url}: ${response.body}`);
  }
  
  // Shorter sleep for stress test
  sleep(Math.random() * 0.5 + 0.1);
}

export function handleSummary(data) {
  logTestSummary('Stress', data.metrics);
  
  return {
    'stdout': JSON.stringify(data, null, 2),
    'stress-test-results.json': JSON.stringify(data, null, 2),
  };
}
