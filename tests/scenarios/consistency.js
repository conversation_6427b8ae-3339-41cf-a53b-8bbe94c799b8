import http from 'k6/http';
import { check, sleep } from 'k6';
import { config } from '../config/config.js';
import { generateEndpoint, logTestSummary } from '../utils/helpers.js';

// Consistency test - Test same endpoints multiple times to check for consistency
export const options = {
  scenarios: {
    consistency_check: {
      executor: 'constant-vus',
      vus: 5,
      duration: '5m',
    }
  },
  thresholds: {
    http_req_duration: ['p(95)<2000'],
    http_req_failed: ['rate<0.05'],
    'consistency_check': ['rate>0.9'], // Custom metric for consistency
  },
  tags: {
    testType: 'consistency'
  }
};

// Store responses for consistency checking
const responseCache = new Map();

export default function () {
  // Test the same endpoint multiple times
  const exchange = config.exchanges[__VU % config.exchanges.length];
  const tradingPair = 'ETH:USDT';
  const amount = 1;
  const symbol = 'ETH';
  
  const url = generateEndpoint(exchange, tradingPair, amount, symbol);
  const cacheKey = `${exchange}_${tradingPair}_${amount}_${symbol}`;
  
  const response = http.get(url, {
    timeout: config.timeout,
    tags: { 
      exchange: exchange,
      consistency_test: 'true'
    }
  });
  
  let isConsistent = true;
  
  const basicChecks = check(response, {
    'Status is 200': (r) => r.status === 200,
    'Response time < 2000ms': (r) => r.timings.duration < 2000,
    'Valid JSON response': (r) => {
      try {
        JSON.parse(r.body);
        return true;
      } catch (e) {
        return false;
      }
    }
  });
  
  if (basicChecks && response.status === 200) {
    try {
      const currentData = JSON.parse(response.body);
      
      if (responseCache.has(cacheKey)) {
        const previousData = responseCache.get(cacheKey);
        
        // Check if response structure is consistent
        const structureConsistent = 
          typeof currentData === typeof previousData &&
          (typeof currentData === 'number' || 
           (typeof currentData === 'object' && 
            Object.keys(currentData).length > 0));
        
        if (!structureConsistent) {
          console.log(`Structure inconsistency for ${exchange}: ${JSON.stringify(previousData)} vs ${JSON.stringify(currentData)}`);
          isConsistent = false;
        }
        
        // For price data, check if values are reasonable (not wildly different)
        if (typeof currentData === 'number' && typeof previousData === 'number') {
          const percentDiff = Math.abs((currentData - previousData) / previousData) * 100;
          if (percentDiff > 50) { // More than 50% difference might indicate an issue
            console.log(`Large price difference for ${exchange}: ${previousData} vs ${currentData} (${percentDiff.toFixed(2)}%)`);
          }
        }
      }
      
      // Update cache with current response
      responseCache.set(cacheKey, currentData);
      
    } catch (e) {
      console.log(`Error parsing response for consistency check: ${e.message}`);
      isConsistent = false;
    }
  }
  
  // Custom metric for consistency
  check(response, {
    'Response is consistent': () => isConsistent
  });
  
  sleep(2); // Wait 2 seconds between requests to same endpoint
}

export function handleSummary(data) {
  logTestSummary('Consistency', data.metrics);
  
  // Report on consistency findings
  console.log('\n=== Consistency Test Analysis ===');
  console.log(`Total endpoints tested: ${responseCache.size}`);
  console.log('Cached responses per exchange:');
  
  const exchangeCounts = {};
  for (const key of responseCache.keys()) {
    const exchange = key.split('_')[0];
    exchangeCounts[exchange] = (exchangeCounts[exchange] || 0) + 1;
  }
  
  Object.entries(exchangeCounts).forEach(([exchange, count]) => {
    console.log(`  ${exchange}: ${count} unique endpoints`);
  });
  
  return {
    'stdout': JSON.stringify(data, null, 2),
    'consistency-test-results.json': JSON.stringify({
      summary: data,
      cacheSize: responseCache.size,
      exchangeCounts: exchangeCounts
    }, null, 2),
  };
}
