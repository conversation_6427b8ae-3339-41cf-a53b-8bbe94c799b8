import http from 'k6/http';
import { check, sleep } from 'k6';
import { config } from '../config/config.js';
import { generateEndpoint, logTestSummary } from '../utils/helpers.js';

// Exchange coverage test - Systematically test all exchanges
export const options = {
  scenarios: {
    exchange_coverage: {
      executor: 'per-vu-iterations',
      vus: config.exchanges.length, // One VU per exchange
      iterations: 10, // 10 iterations per exchange
      maxDuration: '10m',
    }
  },
  thresholds: {
    http_req_duration: ['p(95)<3000'],
    http_req_failed: ['rate<0.1'],
  },
  tags: {
    testType: 'coverage'
  }
};

export default function () {
  // Each VU tests a specific exchange
  const exchangeIndex = (__VU - 1) % config.exchanges.length;
  const exchange = config.exchanges[exchangeIndex];
  
  // Test different trading pairs and amounts for this exchange
  const tradingPair = config.tradingPairs[__ITER % config.tradingPairs.length];
  const amount = config.amounts[__ITER % config.amounts.length];
  const symbol = config.symbols[__ITER % config.symbols.length];
  
  const url = generateEndpoint(exchange, tradingPair, amount, symbol);
  
  console.log(`VU ${__VU} (${exchange}) - Iteration ${__ITER}: ${url}`);
  
  const response = http.get(url, {
    timeout: config.timeout,
    tags: { 
      exchange: exchange,
      tradingPair: tradingPair,
      amount: amount.toString(),
      symbol: symbol
    }
  });
  
  // Detailed checks per exchange
  const checks = check(response, {
    [`${exchange} - Status is 200`]: (r) => r.status === 200,
    [`${exchange} - Response time < 3000ms`]: (r) => r.timings.duration < 3000,
    [`${exchange} - Has response body`]: (r) => r.body && r.body.length > 0,
    [`${exchange} - Valid JSON response`]: (r) => {
      try {
        JSON.parse(r.body);
        return true;
      } catch (e) {
        console.log(`${exchange} - Invalid JSON: ${r.body}`);
        return false;
      }
    },
    [`${exchange} - Contains expected data structure`]: (r) => {
      try {
        const data = JSON.parse(r.body);
        // Check if response contains price-related information
        return data && (
          data.price !== undefined || 
          data.total !== undefined || 
          data.amount !== undefined ||
          data.cost !== undefined ||
          typeof data === 'number' // Some APIs might return just a number
        );
      } catch (e) {
        return false;
      }
    }
  });
  
  // Log failures for debugging
  if (!checks[`${exchange} - Status is 200`]) {
    console.log(`${exchange} failed with status ${response.status}: ${response.body}`);
  }
  
  // Stagger requests to avoid overwhelming the server
  sleep(1 + Math.random());
}

export function handleSummary(data) {
  logTestSummary('Exchange Coverage', data.metrics);
  
  // Create a detailed report by exchange
  const exchangeReport = {};
  
  // Process metrics by exchange tag
  if (data.metrics.http_req_duration && data.metrics.http_req_duration.values) {
    config.exchanges.forEach(exchange => {
      exchangeReport[exchange] = {
        tested: true,
        // Note: k6 doesn't provide easy access to tag-filtered metrics in handleSummary
        // This would need to be implemented with custom metrics if detailed per-exchange stats are needed
      };
    });
  }
  
  return {
    'stdout': JSON.stringify(data, null, 2),
    'exchange-coverage-results.json': JSON.stringify({
      summary: data,
      exchangeReport: exchangeReport
    }, null, 2),
  };
}
