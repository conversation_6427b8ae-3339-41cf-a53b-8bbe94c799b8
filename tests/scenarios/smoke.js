import http from 'k6/http';
import { check, sleep } from 'k6';
import { config } from '../config/config.js';
import { generateEndpointsForAllExchanges, logTestSummary } from '../utils/helpers.js';

// Smoke test - Basic functionality test with minimal load
export const options = {
  vus: 1, // 1 virtual user
  duration: '1m', // Run for 1 minute
  thresholds: config.thresholds.smoke,
  tags: {
    testType: 'smoke'
  }
};

export default function () {
  // Test each exchange once per iteration
  const endpoints = generateEndpointsForAllExchanges();
  
  endpoints.forEach(({ exchange, url }) => {
    console.log(`Testing ${exchange}: ${url}`);
    
    const response = http.get(url, {
      timeout: config.timeout,
      tags: { exchange: exchange }
    });
    
    // Basic checks
    check(response, {
      [`${exchange} - Status is 200`]: (r) => r.status === 200,
      [`${exchange} - Response time < 2000ms`]: (r) => r.timings.duration < 2000,
      [`${exchange} - Response has body`]: (r) => r.body && r.body.length > 0,
      [`${exchange} - Response is valid JSON`]: (r) => {
        try {
          JSON.parse(r.body);
          return true;
        } catch (e) {
          return false;
        }
      },
    });
    
    // Small delay between requests to same exchange
    sleep(0.5);
  });
  
  // Delay between iterations
  sleep(2);
}

export function handleSummary(data) {
  logTestSummary('Smoke', data.metrics);
  return {
    'stdout': JSON.stringify(data, null, 2),
  };
}
