import http from 'k6/http';
import { check, sleep } from 'k6';
import { config } from '../config/config.js';
import { generateRandomEndpoint, createRampUpScenario, logTestSummary } from '../utils/helpers.js';

// Load test - Normal expected load with gradual ramp-up
export const options = {
  scenarios: createRampUpScenario('load_test', 50, '10m'), // Max 50 VUs for 10 minutes
  thresholds: config.thresholds.load,
  tags: {
    testType: 'load'
  }
};

export default function () {
  // Generate random endpoint for this request
  const url = generateRandomEndpoint();
  
  const response = http.get(url, {
    timeout: config.timeout,
    tags: { 
      endpoint: url.split('/').slice(-2).join('/') // Tag with exchange/pair info
    }
  });
  
  // Comprehensive checks
  check(response, {
    'Status is 200': (r) => r.status === 200,
    'Response time < 3000ms': (r) => r.timings.duration < 3000,
    'Response has body': (r) => r.body && r.body.length > 0,
    'Response is valid JSON': (r) => {
      try {
        const data = JSON.parse(r.body);
        return typeof data === 'object';
      } catch (e) {
        return false;
      }
    },
    'Response contains price data': (r) => {
      try {
        const data = JSON.parse(r.body);
        // Assuming the API returns some price-related field
        return data && (data.price !== undefined || data.total !== undefined || data.amount !== undefined);
      } catch (e) {
        return false;
      }
    }
  });
  
  // Random sleep between 0.5 and 2 seconds to simulate real user behavior
  sleep(Math.random() * 1.5 + 0.5);
}

export function handleSummary(data) {
  logTestSummary('Load', data.metrics);
  
  // Save detailed results to file
  return {
    'stdout': JSON.stringify(data, null, 2),
    'load-test-results.json': JSON.stringify(data, null, 2),
  };
}
