import http from 'k6/http';
import { check, sleep } from 'k6';
import { config } from '../config/config.js';
import { generateRandomEndpoint, createSpikeScenario, logTestSummary } from '../utils/helpers.js';

// Spike test - Sudden traffic spikes to test resilience
export const options = {
  scenarios: createSpikeScenario('spike_test', 20, 100, '3m'), // Normal: 20 VUs, Spike: 100 VUs for 3 minutes
  thresholds: config.thresholds.spike,
  tags: {
    testType: 'spike'
  }
};

export default function () {
  const url = generateRandomEndpoint();
  
  const response = http.get(url, {
    timeout: config.timeout,
    tags: { 
      endpoint: url.split('/').slice(-2).join('/'),
      vu: __VU,
      iteration: __ITER,
      stage: __ENV.K6_STAGE || 'unknown'
    }
  });
  
  // Checks for spike test - focus on availability
  check(response, {
    'Service is available (not 503)': (r) => r.status !== 503,
    'Response time < 10000ms': (r) => r.timings.duration < 10000,
    'No timeout errors': (r) => r.status !== 0,
    'Status is success or manageable error': (r) => r.status === 200 || (r.status >= 400 && r.status < 500),
  });
  
  // Log critical errors
  if (response.status === 503 || response.status === 0) {
    console.log(`Critical error ${response.status} for ${url} at VU ${__VU}, iteration ${__ITER}`);
  }
  
  // Very short sleep during spike test
  sleep(Math.random() * 0.3 + 0.1);
}

export function handleSummary(data) {
  logTestSummary('Spike', data.metrics);
  
  return {
    'stdout': JSON.stringify(data, null, 2),
    'spike-test-results.json': JSON.stringify(data, null, 2),
  };
}
