import { config } from '../config/config.js';

// Utility functions for k6 tests

/**
 * Get a random element from an array
 */
export function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Generate a random API endpoint URL
 */
export function generateRandomEndpoint() {
  const exchange = getRandomElement(config.exchanges);
  const tradingPair = getRandomElement(config.tradingPairs);
  const amount = getRandomElement(config.amounts);
  const symbol = getRandomElement(config.symbols);
  
  return `${config.baseUrl}/${exchange}/${tradingPair}:${amount}:${symbol}`;
}

/**
 * Generate a specific endpoint URL
 */
export function generateEndpoint(exchange, tradingPair, amount, symbol) {
  return `${config.baseUrl}/${exchange}/${tradingPair}:${amount}:${symbol}`;
}

/**
 * Generate endpoints for all exchanges with the same parameters
 */
export function generateEndpointsForAllExchanges(tradingPair = 'ETH:USDT', amount = 2, symbol = 'ETH') {
  return config.exchanges.map(exchange => ({
    exchange,
    url: generateEndpoint(exchange, tradingPair, amount, symbol)
  }));
}

/**
 * Create a weighted scenario for gradual ramp-up
 */
export function createRampUpScenario(name, maxVUs, duration) {
  return {
    [name]: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: Math.floor(maxVUs * 0.1) }, // Ramp up to 10%
        { duration: '5m', target: Math.floor(maxVUs * 0.5) }, // Ramp up to 50%
        { duration: '10m', target: maxVUs },                   // Ramp up to 100%
        { duration: duration, target: maxVUs },                // Stay at 100%
        { duration: '5m', target: Math.floor(maxVUs * 0.5) }, // Ramp down to 50%
        { duration: '2m', target: 0 },                        // Ramp down to 0
      ],
    }
  };
}

/**
 * Create a spike scenario
 */
export function createSpikeScenario(name, normalVUs, spikeVUs, spikeDuration = '2m') {
  return {
    [name]: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: normalVUs },    // Ramp up to normal load
        { duration: '5m', target: normalVUs },    // Stay at normal load
        { duration: '10s', target: spikeVUs },    // Spike up
        { duration: spikeDuration, target: spikeVUs }, // Stay at spike
        { duration: '10s', target: normalVUs },   // Drop back to normal
        { duration: '5m', target: normalVUs },    // Stay at normal
        { duration: '2m', target: 0 },           // Ramp down
      ],
    }
  };
}

/**
 * Log test results summary
 */
export function logTestSummary(testName, data) {
  console.log(`\n=== ${testName} Test Summary ===`);
  console.log(`Total Requests: ${data.http_reqs && data.http_reqs.count ? data.http_reqs.count : 'N/A'}`);
  console.log(`Failed Requests: ${data.http_req_failed && data.http_req_failed.rate !== undefined ? (data.http_req_failed.rate * 100).toFixed(2) + '%' : 'N/A'}`);
  console.log(`Average Response Time: ${data.http_req_duration && data.http_req_duration.avg ? data.http_req_duration.avg.toFixed(2) + 'ms' : 'N/A'}`);
  console.log(`95th Percentile: ${data.http_req_duration && data.http_req_duration['p(95)'] ? data.http_req_duration['p(95)'].toFixed(2) + 'ms' : 'N/A'}`);
  console.log(`Requests/sec: ${data.http_reqs && data.http_reqs.rate ? data.http_reqs.rate.toFixed(2) : 'N/A'}`);
  console.log('================================\n');
}
