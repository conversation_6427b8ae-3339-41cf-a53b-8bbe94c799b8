#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Available test types
const testTypes = {
  smoke: 'tests/scenarios/smoke.js',
  load: 'tests/scenarios/load.js',
  stress: 'tests/scenarios/stress.js',
  spike: 'tests/scenarios/spike.js',
  coverage: 'tests/scenarios/coverage.js',
  consistency: 'tests/scenarios/consistency.js'
};

function printUsage() {
  console.log('\n🚀 Crypto Monitor Load Test Runner\n');
  console.log('Usage: node run-tests.js [test-type] [options]\n');
  console.log('Available test types:');
  Object.keys(testTypes).forEach(type => {
    console.log(`  ${type.padEnd(12)} - ${getTestDescription(type)}`);
  });
  console.log('\nOptions:');
  console.log('  --verbose    Show detailed k6 output');
  console.log('  --quiet      Minimal output');
  console.log('  --help       Show this help message');
  console.log('\nExamples:');
  console.log('  node run-tests.js smoke');
  console.log('  node run-tests.js load --verbose');
  console.log('  node run-tests.js all');
  console.log('');
}

function getTestDescription(type) {
  const descriptions = {
    smoke: 'Basic functionality test (1 min)',
    load: 'Normal load simulation (24 min)',
    stress: 'High load stress test (39 min)',
    spike: 'Traffic spike test (17 min)',
    coverage: 'All exchanges test (10 min)',
    consistency: 'Response consistency test (5 min)'
  };
  return descriptions[type] || 'Unknown test';
}

function runK6Test(testFile, options = {}) {
  return new Promise((resolve, reject) => {
    const args = ['run'];
    
    if (options.verbose) {
      args.push('--verbose');
    } else if (options.quiet) {
      args.push('--quiet');
    }
    
    args.push(testFile);
    
    console.log(`\n🧪 Running ${path.basename(testFile)}...`);
    console.log(`📊 Command: k6 ${args.join(' ')}\n`);
    
    const k6Process = spawn('k6', args, {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    k6Process.on('close', (code) => {
      if (code === 0) {
        console.log(`\n✅ ${path.basename(testFile)} completed successfully\n`);
        resolve();
      } else {
        console.log(`\n❌ ${path.basename(testFile)} failed with exit code ${code}\n`);
        reject(new Error(`Test failed with exit code ${code}`));
      }
    });
    
    k6Process.on('error', (error) => {
      console.error(`\n❌ Failed to start k6: ${error.message}`);
      console.error('Make sure k6 is installed: https://k6.io/docs/getting-started/installation/\n');
      reject(error);
    });
  });
}

async function runTests(testType, options = {}) {
  const startTime = Date.now();
  
  try {
    if (testType === 'all') {
      console.log('🚀 Running essential test suite...\n');
      await runK6Test(testTypes.smoke, options);
      await runK6Test(testTypes.coverage, options);
      await runK6Test(testTypes.load, options);
    } else if (testType === 'full') {
      console.log('🚀 Running complete test suite...\n');
      for (const [type, file] of Object.entries(testTypes)) {
        await runK6Test(file, options);
      }
    } else if (testTypes[testType]) {
      await runK6Test(testTypes[testType], options);
    } else {
      console.error(`❌ Unknown test type: ${testType}`);
      printUsage();
      process.exit(1);
    }
    
    const duration = Math.round((Date.now() - startTime) / 1000);
    console.log(`🎉 All tests completed successfully in ${duration}s`);
    
  } catch (error) {
    const duration = Math.round((Date.now() - startTime) / 1000);
    console.error(`💥 Tests failed after ${duration}s`);
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const testType = args[0];
const options = {
  verbose: args.includes('--verbose'),
  quiet: args.includes('--quiet'),
  help: args.includes('--help')
};

if (!testType || options.help) {
  printUsage();
  process.exit(0);
}

// Check if k6 is available
const { spawn: spawnSync } = require('child_process');
const k6Check = spawnSync('k6', ['version'], { stdio: 'pipe' });
k6Check.on('error', () => {
  console.error('❌ k6 is not installed or not in PATH');
  console.error('Please install k6: https://k6.io/docs/getting-started/installation/');
  process.exit(1);
});

runTests(testType, options);
