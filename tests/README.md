# Crypto Monitor Load Testing

This project contains comprehensive load testing for the crypto exchanges API aggregator using k6.

## API Under Test

The API being tested is a crypto exchanges aggregator that provides real-time pricing data:
- **Base URL**: `http://159.69.187.66:3001`
- **Endpoint Format**: `/{exchange}/{trading_pair}:{amount}:{symbol}`
- **Example**: `http://159.69.187.66:3001/binance/ETH:USDT:2:ETH`

### Supported Exchanges
- binance
- bybit
- coinbase
- okx
- bitget
- mexc
- gate
- htx
- kucoin
- kraken
- bitfinex
- bitmart
- exmo

## Test Types

### 1. Smoke Test (`test:smoke`)
- **Purpose**: Basic functionality verification
- **Load**: 1 virtual user for 1 minute
- **Coverage**: Tests each exchange once per iteration
- **Thresholds**: 
  - 95% of requests < 2000ms
  - < 10% failure rate

### 2. Exchange Coverage Test (`test:coverage`)
- **Purpose**: Systematic testing of all exchanges
- **Load**: One VU per exchange, 10 iterations each
- **Coverage**: Tests different trading pairs and amounts per exchange
- **Duration**: Max 10 minutes

### 3. Load Test (`test:load`)
- **Purpose**: Normal expected load simulation
- **Load**: Gradual ramp-up to 50 virtual users
- **Duration**: 24 minutes total (including ramp-up/down)
- **Pattern**: Realistic user behavior with random delays
- **Thresholds**:
  - 95% of requests < 3000ms
  - < 5% failure rate
  - > 10 requests/second

### 4. Stress Test (`test:stress`)
- **Purpose**: Find breaking point beyond normal capacity
- **Load**: Gradual ramp-up to 200 virtual users
- **Duration**: 39 minutes total
- **Thresholds**:
  - 95% of requests < 5000ms
  - < 10% failure rate

### 5. Spike Test (`test:spike`)
- **Purpose**: Test resilience to sudden traffic spikes
- **Load**: Normal 20 VUs with spikes to 100 VUs
- **Pattern**: Sudden traffic increases and decreases
- **Thresholds**:
  - 95% of requests < 10000ms
  - < 20% failure rate

## Running Tests

### Prerequisites
```bash
npm install
```

### Individual Tests
```bash
# Quick smoke test (1 minute)
npm run test:smoke

# Test all exchanges systematically (10 minutes)
npm run test:coverage

# Response consistency test (5 minutes)
npm run test:consistency

# Standard load test (24 minutes)
npm run test:load

# Stress test (39 minutes)
npm run test:stress

# Spike test (17 minutes)
npm run test:spike
```

### Test Suites
```bash
# Essential tests (smoke + coverage + load)
npm run test:all

# Complete test suite (all tests)
npm run test:full
```

### Using the Test Runner
```bash
# Interactive test runner with better output
npm run test:runner smoke
npm run test:runner load --verbose
npm run test:runner all

# Show help
npm run test:help

# Run from tests directory
cd tests
node scripts/run-tests.js smoke
```

## Test Configuration

Configuration is centralized in `tests/config/config.js`:

- **Base URL**: API endpoint
- **Exchanges**: List of supported exchanges
- **Trading Pairs**: Popular crypto pairs to test
- **Amounts**: Different order sizes
- **Symbols**: Various cryptocurrencies
- **Thresholds**: Performance criteria for each test type

## Results and Reporting

### Console Output
Each test provides real-time metrics and a summary including:
- Total requests
- Failure rate
- Average response time
- 95th percentile response time
- Requests per second

### Result Files
- `load-test-results.json` - Detailed load test results
- `stress-test-results.json` - Detailed stress test results  
- `spike-test-results.json` - Detailed spike test results
- `exchange-coverage-results.json` - Exchange-specific results

## Understanding Results

### Key Metrics
- **http_req_duration**: Response time metrics
- **http_req_failed**: Failure rate
- **http_reqs**: Request rate (requests/second)
- **vus**: Number of virtual users

### Success Criteria
- **Smoke Test**: Basic functionality works
- **Load Test**: Handles expected traffic smoothly
- **Stress Test**: Degrades gracefully under high load
- **Spike Test**: Recovers from traffic spikes

### Warning Signs
- High failure rates (>5% for normal load)
- Response times consistently above thresholds
- 5xx server errors
- Connection timeouts

## Customization

### Modifying Load Patterns
Edit the scenarios in each test file to adjust:
- Number of virtual users
- Test duration
- Ramp-up patterns

### Adding New Exchanges
Update `config.js` to add new exchanges to the `exchanges` array.

### Changing Test Parameters
Modify `config.js` to adjust:
- Trading pairs
- Amount ranges
- Timeout values
- Performance thresholds

## Troubleshooting

### Common Issues
1. **Connection refused**: Ensure the API server is running
2. **High failure rates**: Check server capacity and network
3. **Timeout errors**: Increase timeout in config or check server performance

### Debug Mode
Add `--verbose` flag to k6 commands for detailed logging:
```bash
k6 run --verbose tests/scenarios/load.js
```
