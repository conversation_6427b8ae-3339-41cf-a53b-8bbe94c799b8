// Configuration for k6 load tests
export const config = {
  // Base URL of your crypto API
  baseUrl: 'http://159.69.187.66:3001',
  
  // Supported exchanges
  exchanges: [
    'binance',
    'bybit',
    'coinbase',
    'okx',
    'bitget',
    'mexc',
    'gate',
    'htx',
    'kucoin',
    'kraken',
    'bitfinex',
    'bitmart',
    'exmo'
  ],
  
  // Popular trading pairs to test with
  tradingPairs: [
    'ETH:USDT',
    'BTC:USDT',
    'BNB:USDT',
    'ADA:USDT',
    'SOL:USDT',
    'MATIC:USDT',
    'DOT:USDT',
    'AVAX:USDT',
    'LINK:USDT',
    'UNI:USDT'
  ],
  
  // Amount ranges to test
  amounts: [0.1, 0.5, 1, 2, 5, 10],
  
  // Symbols to use in requests
  symbols: ['ETH', 'BTC', 'BNB', 'ADA', 'SOL'],
  
  // Request timeouts
  timeout: '30s',
  
  // Thresholds for different test types
  thresholds: {
    smoke: {
      http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
      http_req_failed: ['rate<0.1'],     // Less than 10% failures
    },
    load: {
      http_req_duration: ['p(95)<3000'], // 95% of requests under 3s
      http_req_failed: ['rate<0.05'],    // Less than 5% failures
      http_reqs: ['rate>10'],            // At least 10 requests per second
    },
    stress: {
      http_req_duration: ['p(95)<5000'], // 95% of requests under 5s
      http_req_failed: ['rate<0.1'],     // Less than 10% failures
    },
    spike: {
      http_req_duration: ['p(95)<10000'], // 95% of requests under 10s
      http_req_failed: ['rate<0.2'],      // Less than 20% failures
    }
  }
};
