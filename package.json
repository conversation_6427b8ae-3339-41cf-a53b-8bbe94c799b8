{"name": "crypto-monitor", "version": "1.0.0", "license": "CC0-1.0", "author": {"name": "serhii"}, "main": "./out/main.js", "engines": {"node": ">=20.0.0"}, "scripts": {"clean": "rimraf ./out", "build": "npm run clean && tsc -p ./", "watch": "tsc -watch -p ./", "lint": "eslint src --ext ts", "lint:fix": "eslint src --ext ts --fix", "dev": "npm run build && node --enable-source-maps ./out/main.js", "prestart": "npm run build", "start": "node --trace-warnings ./out/main.js", "pretest": "npm run lint", "pm2:deploy": "npm run build && pm2 startOrReload ecosystem.config.js", "pm2:start": "pm2 start ecosystem.config.js --log ./logs/", "pm2:stop": "pm2 stop crypto-monitor", "pm2:restart": "pm2 restart crypto-monitor", "pm2:reload": "pm2 reload crypto-monitor", "pm2:delete": "pm2 delete crypto-monitor", "pm2:logs": "pm2 logs crypto-monitor", "pm2:monit": "pm2 monit", "pm2:list": "pm2 list", "test:smoke": "k6 run tests/scenarios/smoke.js", "test:load": "k6 run tests/scenarios/load.js", "test:stress": "k6 run tests/scenarios/stress.js", "test:spike": "k6 run tests/scenarios/spike.js", "test:coverage": "k6 run tests/scenarios/coverage.js", "test:consistency": "k6 run tests/scenarios/consistency.js", "test:all": "npm run test:smoke && npm run test:coverage && npm run test:load", "test:full": "npm run test:smoke && npm run test:coverage && npm run test:consistency && npm run test:load && npm run test:stress && npm run test:spike", "test:runner": "node tests/scripts/run-tests.js", "test:help": "node tests/scripts/run-tests.js --help"}, "devDependencies": {"@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.34.0", "eslint": "^9.28.0", "eslint-plugin-unicorn": "^59.0.1", "pino-pretty": "^13.0.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@esfx/async-deferred": "^1.0.0", "ccxt": "^4.4.77", "dotenv": "^16.5.0", "pino": "^9.6.0"}}